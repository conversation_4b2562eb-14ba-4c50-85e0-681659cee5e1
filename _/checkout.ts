#!/usr/bin/env ts-node

import { execSync } from 'child_process';
import { Command } from 'commander';
import { userInfo } from 'os';
import { env, exit } from 'process';

const program = new Command();

const changeType = new Set([
  'feat',
  'docs',
  'fix',
  'refactor',
  'style',
  'test',
  'chore',
]);

function createBranch(type: string, user: string, name: string) {
  execSync('git checkout main');
  execSync('git pull');
  execSync(`git checkout -b ${user}/${type}/${name}`);
}

// Gets username from the os
function getUserOS() {
  return (env.BRANCH_USER || userInfo().username).split(' ')[0];
}

interface Options {
  user: string | undefined | null;
}

program
  .version('1.0.0', '-v, --version')
  .description(
    'Create properly formatted git branch names example:\n' +
      'checkout feat add-authentication     "new branch named {your_name}/feat/add-authentication"'
  )
  .argument(
    '<type>',
    'Change type: feat, docs, fix, refactor, style, test, chore'
  )
  .argument('<name>', 'name of change... ie: {ticket-number}{task}')
  .option('-u, --user <user>', 'Set a custom user name')
  .action((type: string, name: string, { user }: Options) => {
    // Check that the type is actually in the allowed types
    if (!changeType.has(type)) {
      const humanReadChangeType = Array.from(changeType).join(', ');
      console.error(
        `${type} is not within the valid change types: ${humanReadChangeType}`
      );
      exit(1);
    }
    // If user specified use that else use the user inferred from the os
    user = user || getUserOS();

    createBranch(type, user, name);
  });

program.parse(process.argv);

#!/bin/bash
echo "Installing Bain git tools..."

SCRIPT_DIR=$( cd -- "$( dirname -- "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )
scripts="$SCRIPT_DIR/git-*"
# path_base="$(uname -s | grep -i "darwin" &> /dev/null && echo -n "/usr/bin" || echo -n "/usr/local/bin")"
[[ ! -z "${PATH_BASE}" ]] && path_base="${PATH_BASE}" || path_base="/usr/local/bin"
[[ ! -d $path_base ]] && sudo mkdir $path_base && echo "Made directory $path_base if not in your path please put this in your path. This is also owned by root.. if this is not what you want please correct this"

for script in $scripts
do
    script_name="$(basename $script)"
    echo "Installing $script_name to $path_base"
    if [[ -f $path_base/$script_name ]]; then
        while true; do
            read -p "Do you wish to replace $path_base/$script_name [y/n]: " yn
            case $yn in
                [Yy]* ) sudo cp $script $path_base; break;;
                [Nn]* ) break;;
                * ) echo "Please answer yes or no.";;
            esac
        done
    else
        sudo cp $script $path_base
    fi
done

echo "You may now use Bain git commands such as git feat, git fix, git docs, git refactor"

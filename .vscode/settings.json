{
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",

  "[python]": {
    "editor.defaultFormatter": "ms-python.black-formatter",
    "editor.formatOnSave": true
  },
  "prettier.requireConfig": true,

  // Python LSP
  "python.languageServer": "Pylance",
  "python.analysis.autoSearchPaths": true,
  "python.analysis.useLibraryCodeForTypes": true,

  // Python specific Analysis and Linting
  "python.analysis.typeCheckingMode": "basic",
  "python.autoComplete.extraPaths": ["apps/backend/src"],
  "python.analysis.extraPaths": ["apps/backend/src"],
  "python.envFile": "${workspaceFolder}/.env",
  "pylint.args": ["--rcfile=apps/backend/.pylintrc"],
  "pylint.importStrategy": "fromEnvironment",
  "files.exclude": {
    "**/__pycache__": true,
    "**/*.pyc": true
  }
}

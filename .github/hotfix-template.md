# Hotfix Pull Request Template

## Hotfix Title

[Provide a clear title, e.g., "Urgent fix for critical login error". Also, please add one of these prefixes - build,chore,ci,docs,feat,fix,perf,refactor,revert,style,test]

## Related Ticket/Story

[Detail the of the related ticket on ProductBoard/JIRA...]

## Type of Change

- [x] Hotfix (urgent change which fixes an issue in production)

## Description of the Hotfix

[Detail the issue being fixed and the level of urgency.]

## Impact of the Hotfix

[Discuss the immediate impact on the system and any potential side effects.]

## Testing Conducted

[Detail the testing done to ensure the hotfix resolves the issue without causing additional problems.]

## Additional Remarks

[Notes for reviewers, including any potential risks involved.]

## Checklist

- [ ] The hotfix quickly and effectively addresses the critical issue.
- [ ] I have tested the hotfix thoroughly in a production-like environment.
- [ ] The hotfix does not introduce new bugs in the system.
- [ ] I have documented the issue and the urgency of the hotfix.
- [ ] All necessary stakeholders have been informed about the hotfix.
- [ ] The hotfix is ready for immediate deployment upon approval.

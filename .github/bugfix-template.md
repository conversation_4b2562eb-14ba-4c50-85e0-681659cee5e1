# Bug Fix Pull Request Template

## Bug Fix Title

[Provide a succinct and clear title for the bug fix, e.g., "Resolve crash when loading extensive files", Also, please add one of these prefixes - build,chore,ci,docs,feat,fix,perf,refactor,revert,style,test]

## Related Ticket/Story

[Detail the of the related ticket on ProductBoard/JIRA...]

## Type of Change

- [x] Bug fix (non-breaking alteration which rectifies an issue)

## Description of the Bug

[Brief overview of the bug being remedied. Elucidate what the issue was and the context in which it emerged.]

## Steps to Reproduce

1. [Step 1]
2. [Step 2]
3. [Step 3]

[Supply clear steps to replicate the bug. This assists in comprehending and corroborating the bug fix.]

## Description of the Bug Fix

[Describe how you have rectified the bug. Expound on the approach you adopted and why it is efficacious.]

## Impact of the Bug Fix

[Discuss the repercussions of the bug fix on existing functionality. Ensure to accentuate any alterations in behaviour, performance, or side-effects.]

## Testing Performed

[Detail the tests you have executed to ensure that the bug fix functions as anticipated and does not impinge on other parts of the application.]

## Screenshots/Videos (if applicable)

[Attach screenshots or videos demonstrating the bug prior to and following the fix.]

## Additional Comments

[Any supplementary comments or information that might be pertinent for the reviewers.]

## Checklist

- [ ] My code adheres to the coding and style norms of this project.
- [ ] I have conducted a self-analysis of my code to ascertain it meets quality benchmarks.
- [ ] I have annotated my code, particularly in sections that might be challenging to comprehend.
- [ ] I have exhaustively tested my amendments in diverse scenarios and settings (local/CI/CD).
- [ ] I have instituted or refreshed unit and integration tests to substantiate my changes.
- [ ] I have verified that my amendments do not introduce new cautions or errors.
- [ ] My alterations function effectively and as anticipated, corroborated by the added tests.
- [ ] I have revised the documentation correspondingly, including READMEs and docstrings.
- [ ] Current and emergent unit tests pass locally with my alterations.
- [ ] Any reliant alterations have been amalgamated and published in downstream modules, if applicable.
- [ ] I have scrutinised and rectified any potential performance issues.
- [ ] I have authenticated that my alterations are compatible across various platforms and frameworks.

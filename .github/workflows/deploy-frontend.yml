name: Deploy NX Frontend to Azure Static Website

on:
  push:
    branches:
      - main
      - feat/mostagir/frontend_deploy
    paths:
      - 'apps/frontend/**'
      - 'libs/**'
      - 'workspace.json'
      - 'nx.json'
      - '.github/workflows/**'

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout Repo
        uses: actions/checkout@v3

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '20'
          cache: 'yarn'

      - name: Install Dependencies
        run: yarn install --frozen-lockfile

      - name: Build React App with Environment Variables
        env:
          API_PROTOCOL: https
          APP_HOST: api.4c8cb7f646c2.azure.bain.dev
          APP_PORT: ''
        run: npx nx build frontend

      - name: Upload to Azure Static Website
        run: |
          az storage blob upload-batch \
            --account-name ${{ secrets.AZURE_STORAGE_ACCOUNT }} \
            --account-key ${{ secrets.AZURE_STORAGE_KEY }} \
            --destination '$web' \
            --source dist/apps/frontend \
            --overwrite

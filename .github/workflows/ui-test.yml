name: UI Tests - Chromatic

on:
  workflow_dispatch:
  # uncomment once setup
  push:
    branches:
      - dev
  pull_request:

jobs:
  ui-test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Clear Yarn Cache
        run: yarn cache clean

      - uses: actions/setup-node@v3
        with:
          node-version: 20
          cache: 'yarn'

      - run: yarn install --frozen-lockfile

      - run: yarn nx run-many -t build-storybook

      # uncomment once storybook is setup
      # - name: Publish frontend customer to Chromatic
      #   uses: chromaui/action@v1
      #   with:
      #     projectToken: ${{ secrets.CHROMATIC_PROJECT_TOKEN }}
      #     storybookBuildDir: dist/storybook/frontend-react
      #     exitOnceUploaded: true

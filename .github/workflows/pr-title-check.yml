name: PR Title Check
env:
  HUSKY: 0
on:
  pull_request:
    types: [opened, edited, synchronize, reopened]
permissions:
  pull-requests: write
jobs:
  check-pr-title:
    runs-on: ubuntu-latest
    steps:
      - uses: deepakputhraya/action-pr-title@master
        with:
          allowed_prefixes: 'build,chore,ci,docs,feat,fix,perf,refactor,revert,style,test'
          prefix_case_sensitive: true
          github_token: ${{ github.token }}

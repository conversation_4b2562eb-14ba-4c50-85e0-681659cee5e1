name: Pull request check

on:
  pull_request:
    branches: [dev]

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  pr-check:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0 # Fetch all branches and full history

      - name: Fetch dev branch explicitly
        run: git fetch origin dev:dev # Ensure the dev branch is fetched

      - uses: actions/setup-node@v3
        with:
          node-version: 20
          cache: 'yarn'

      - run: yarn install --frozen-lockfile

      - name: Setup env files
        run: cp .env.example .env

      - name: Run NX Affected
        run: yarn nx affected -t build,lint,test --base=dev

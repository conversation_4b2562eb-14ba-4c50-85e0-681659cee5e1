# Feature Pull Request Template

## Feature Title

[Provide a succinct and clear title for the feature, e.g., "Introduce advanced search functionality". Also, please add one of these prefixes - build,chore,ci,docs,feat,fix,perf,refactor,revert,style,test]

## Related Ticket/Story

[Detail the of the related ticket on ProductBoard/JIRA...]

## Type of Change

- [x] New feature (non-breaking change which adds functionality)

## Description of the Feature

[A brief overview of the feature being added. Explain the functionality and its intended purpose.]

## Implementation Details

[Describe your approach to implementing the feature. Include any design decisions and methods used.]

## Impact of the Feature

[Discuss how this feature affects the existing codebase, including any potential behavioural or performance changes.]

## Testing Conducted

[Detail the tests carried out to ensure the feature works as intended and integrates well with the existing system.]

## Screenshots/Videos (if applicable)

[Attach screenshots or videos demonstrating the new feature in action.]

## Additional Remarks

[Any additional comments or information for the reviewers.]

## Checklist

- [ ] My code adheres to the project's coding and style guidelines.
- [ ] I have conducted a self-review of my code.
- [ ] I have commented my code, particularly in complex areas.
- [ ] I have made corresponding changes to the documentation.
- [ ] I have tested my feature thoroughly in different environments.
- [ ] I have added tests that prove my feature works as intended.
- [ ] New and existing unit tests pass locally with my changes.
- [ ] I have assessed the performance impact of the feature.
- [ ] My changes do not introduce new warnings or errors.
- [ ] I have checked for compatibility with other parts of the codebase.

# Refactor Pull Request Template

## Refactor Title

[Provide a concise and clear title for the refactor, e.g., "Refactor authentication service for improved readability and performance". Also, please add one of these prefixes - build, chore, ci, docs, feat, fix, perf, refactor, revert, style, test]

## Related Ticket/Story

[Provide the link or reference to the related ticket or story on ProductBoard/JIRA if applicable.]

## Type of Change

- [x] Code refactoring (non-breaking changes to improve code structure, readability, or maintainability)
- [ ] Performance improvement

## Description of the Refactor

[A brief summary of the refactor, explaining why it was needed and what it aims to achieve.]

## Refactoring Approach

[Explain the changes made to the code, such as restructuring, renaming, or optimizing functions. Describe any patterns or principles applied (e.g., DRY, SOLID, etc.).]

## Impact of the Refactor

[Discuss how this refactor improves the codebase. Mention any impact on existing functionality, performance, or future maintainability.]

## Testing Conducted

[Detail the tests performed to ensure the refactor does not break any existing functionality and meets its objectives.]

## Screenshots/Diagrams (if applicable)

[Attach relevant visuals, such as before-and-after code snippets, diagrams, or performance metrics.]

## Risks and Mitigations

[List any potential risks associated with the refactor and how they were mitigated.]

## Additional Remarks

[Any additional information for the reviewers.]

## Checklist

- [ ] My code adheres to the project's coding and style guidelines.
- [ ] I have conducted a self-review of my code.
- [ ] I have commented the code where necessary to explain changes.
- [ ] I have updated relevant documentation if applicable.
- [ ] I have tested my changes thoroughly and ensured no existing functionality is broken.
- [ ] New and existing unit tests pass locally with my changes.
- [ ] I have checked for compatibility with other parts of the codebase.
- [ ] I have evaluated the performance impact of my changes.
- [ ] I have removed any unnecessary code or dependencies as part of the refactor.

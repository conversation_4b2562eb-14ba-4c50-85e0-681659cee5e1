-- Performance optimization indexes for fact tables
-- These indexes will significantly improve query performance for the performance service

-- Indexes for fact_channel_contribution table
-- Composite index for date range and store filtering
CREATE INDEX idx_fact_channel_contribution_date_store
ON fact_channel_contribution (date_key_id, store_key_id);

-- Index for media channel filtering
CREATE INDEX idx_fact_channel_contribution_media_channel
ON fact_channel_contribution (media_channel_key_id);

-- Index for target filtering
CREATE INDEX idx_fact_channel_contribution_target
ON fact_channel_contribution (target);

-- Composite index for the most common query pattern
CREATE INDEX idx_fact_channel_contribution_composite
ON fact_channel_contribution (date_key_id, store_key_id, media_channel_key_id, target);

-- Indexes for fact_weekly_sales table
-- Composite index for date range and store filtering
CREATE INDEX idx_fact_weekly_sales_date_store
ON fact_weekly_sales (date_key_id, store_key_id);

-- Index for department filtering
CREATE INDEX idx_fact_weekly_sales_department
ON fact_weekly_sales (department_key_id);

-- Composite index for the most common query pattern
CREATE INDEX idx_fact_weekly_sales_composite
ON fact_weekly_sales (date_key_id, store_key_id, department_key_id);

-- Indexes for fact_spend table
-- Composite index for date range and store filtering
CREATE INDEX idx_fact_spend_date_store
ON fact_spend (date_key_id, store_key_id);

-- Index for channel filtering
CREATE INDEX idx_fact_spend_channel
ON fact_spend (channel_key_id);

-- Composite index for the most common query pattern
CREATE INDEX idx_fact_spend_composite
ON fact_spend (date_key_id, store_key_id, channel_key_id, department_key_id);

-- Indexes for fact_response_curve table
-- Index for media channel filtering
CREATE INDEX idx_fact_response_curve_media_channel
ON fact_response_curve (media_channel_key_id);

-- Index for store filtering
CREATE INDEX idx_fact_response_curve_store
ON fact_response_curve (store_key);

-- Index for target filtering
CREATE INDEX idx_fact_response_curve_target
ON fact_response_curve (target);

-- Composite index for the most common query pattern
CREATE INDEX idx_fact_response_curve_composite
ON fact_response_curve (media_channel_key_id, store_key, target);

-- Indexes for dimension tables (if not already present)
-- Index for dim_channel
CREATE INDEX idx_dim_channel_name
ON dim_channel (media_channel_name);

-- Index for dim_date
CREATE INDEX idx_dim_date_date_key
ON dim_date (date_key);

-- Index for dim_store
CREATE INDEX idx_dim_store_store_key
ON dim_store (store_key);

-- Additional covering indexes for better performance
-- Note: MySQL doesn't support INCLUDE clause, so these are regular composite indexes
-- Covering index for fact_channel_contribution with spend
CREATE INDEX idx_fact_channel_contribution_covering
ON fact_channel_contribution (date_key_id, store_key_id, media_channel_key_id, target, spend);

-- Covering index for fact_weekly_sales with sales
CREATE INDEX idx_fact_weekly_sales_covering
ON fact_weekly_sales (date_key_id, store_key_id, weekly_sales, weekly_transactions);

-- Covering index for fact_spend with spend amount
CREATE INDEX idx_fact_spend_covering
ON fact_spend (date_key_id, store_key_id, channel_key_id, spend);

-- Additional specialized indexes for timeseries query optimization
-- Optimized index for fact_channel_contribution timeseries queries
CREATE INDEX idx_fact_channel_contribution_timeseries
ON fact_channel_contribution (date_key_id, media_channel_key_id, target, store_key_id, spend);

-- Optimized index for fact_spend timeseries queries
CREATE INDEX idx_fact_spend_timeseries
ON fact_spend (date_key_id, channel_key_id, store_key_id, spend);

-- Index for dim_date to optimize period grouping
CREATE INDEX idx_dim_date_period_fields
ON dim_date (date_key, date, year, quarter);

-- Composite index for cross join optimization between dim_date and dim_channel
CREATE INDEX idx_dim_date_range
ON dim_date (date_key, date);

CREATE INDEX idx_dim_channel_optimized
ON dim_channel (media_channel_key, media_channel_name);

-- Partitioning-friendly indexes for large fact tables
-- Index to support date range filtering with store partitioning
CREATE INDEX idx_fact_channel_contribution_date_partition
ON fact_channel_contribution (date_key_id, store_key_id)
USING BTREE;

CREATE INDEX idx_fact_spend_date_partition
ON fact_spend (date_key_id, store_key_id)
USING BTREE;

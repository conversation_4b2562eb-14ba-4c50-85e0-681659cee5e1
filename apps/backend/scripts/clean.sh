#!/bin/bash

# Define safe files and directories
SAFE_ITEMS=(
    ".env"
    ".vscode/"
)

# The script can find images we build with own Dockerfile
# If you have other images, you can add them to the list
IMAGES=(
    "pgvector/pgvector"
)

# Function to confirm with the user
confirm() {
    read -p "$1 (y/N) " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        return 0  # User confirmed
    else
        return 1  # User declined
    fi
}

# Function to delete a directory
delete_dir() {
    if [ -d "$1" ]; then
        if confirm "Are you sure you want to delete directory: $1?"; then
            echo "Deleting directory: $1"
            rm -rf "$1"
        else
            echo "Skipping directory: $1"
        fi
    else
        echo "Directory not found, skipping: $1"
    fi
}

# Function to delete a file
delete_file() {
    if [ -f "$1" ]; then
        if confirm "Are you sure you want to delete file: $1?"; then
            echo "Deleting file: $1"
            rm -f "$1"
        else
            echo "Skipping file: $1"
        fi
    else
        echo "File not found, skipping: $1"
    fi
}

# Function to clean Docker objects
clean_docker() {
    # Get the name of the git repository root folder to use as a prefix
    REPO_NAME=$(basename "$(git rev-parse --show-toplevel)")

    IFS=$'\n'
    for CONTAINER in $(docker ps -a --format '{{.ID}} {{.Image}}' | grep " ${REPO_NAME}"); do
        CONTAINER_ID=$(echo "$CONTAINER" | awk '{print $1}')
        IMAGE_NAME=$(echo "$CONTAINER" | awk '{print $2}')

        # Confirm with the user before deleting each container
        if confirm "Do you want to remove Docker container ${CONTAINER_ID} (Image: ${IMAGE_NAME})?"; then
            docker rm -f "${CONTAINER_ID}"
        else
            echo "Skipping container ${CONTAINER_ID} (Image: ${IMAGE_NAME})"
        fi
    done

    # Remove *containers* from the IMAGES list
    for IMAGE in "${IMAGES[@]}"; do
        # Find all running or stopped containers based on the image
        CONTAINER_IDS=$(docker ps -a --format '{{.ID}} {{.Image}}' | grep " ${REPO_NAME}")

        if [ -z "$CONTAINER_IDS" ]; then
            echo "No containers found for ${IMAGE}."
            continue
        fi

        # Ask for confirmation before removing each container
        for CONTAINER_ID in $CONTAINER_IDS; do
            if confirm "Do you want to remove Docker container ${CONTAINER_ID} for ${IMAGE}?"; then
                docker container rm -f "${CONTAINER_ID}"
            else
                echo "Skipping container ${CONTAINER_ID} for ${IMAGE}"
            fi
        done
    done

    # List all images, filter by the repository name as prefix
    IFS=$'\n'
    for IMAGE in $(docker images --format '{{.Repository}}:{{.Tag}}' | grep "^${REPO_NAME}"); do
        # Confirm with the user before deleting each image
        if confirm "Do you want to remove Docker image ${IMAGE}?"; then
            docker rmi "${IMAGE}"
        else
            echo "Skipping image ${IMAGE}"
        fi
    done
    unset IFS

    # Remove images from the IMAGES list
    for IMAGE in "${IMAGES[@]}"; do
        if confirm "Do you want to remove Docker image ${IMAGE}?"; then
            docker rmi "${IMAGE}"
        else
            echo "Skipping image ${IMAGE}"
        fi
    done
}


# Handle -a option for cleaning Docker objects
if [[ $1 == "-a" ]]; then
    clean_docker
fi

# Find the root of the git repository
REPO_ROOT=$(git rev-parse --show-toplevel)
cd "$REPO_ROOT" || exit

# List ignored files and directories
IGNORED_ITEMS=$(git status --ignored -s | grep '!!' | cut -c 4- | grep '^backend/')

# Loop over ignored items
for ITEM in $IGNORED_ITEMS; do
    IS_SAFE=0
    for SAFE_ITEM in "${SAFE_ITEMS[@]}"; do
        if [[ $ITEM == *"$SAFE_ITEM"* ]]; then
            IS_SAFE=1
            break
        fi
    done

    if [[ $IS_SAFE -eq 0 ]]; then
        if [[ -d $ITEM ]]; then
            # Check if it's the topmost directory of a path
            BASENAME=$(basename "$ITEM")
            if [[ $ITEM != "backend/$BASENAME" ]]; then
                delete_dir "$ITEM"
            else
                echo "Skipping topmost directory: $ITEM"
            fi
        elif [[ -f $ITEM ]]; then
            delete_file "$ITEM"
        fi
    fi
done
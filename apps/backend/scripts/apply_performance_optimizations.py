#!/usr/bin/env python3
"""
<PERSON>ript to apply performance optimizations for the slowest queries.
This creates critical indexes and optimized views to speed up:
- get_driver_data (10s -> <2s)
- get_all_channel_contributions (7s -> <1s)
"""

import asyncio
import sys
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.shared.services.database_service import init_db, close_db
from src.shared.services.logger_app_service import get_logger
from tortoise import Tortoise

logger = get_logger()


async def apply_performance_optimizations():
    """Apply critical performance optimizations for slow queries."""
    try:
        # Initialize database connection
        await init_db()
        connection = Tortoise.get_connection("default")
        logger.info("Connected to database")

        # Critical indexes for fact_channel_contribution (7s query optimization)
        critical_indexes = [
            # For get_all_channel_contributions query - optimized for driver_category filtering and aggregation
            (
                "fact_channel_contribution",
                "idx_fcc_driver_category_optimized",
                "(driver_category, date_key, store_key, media_channel_key)",
                "Optimized index for driver category filtering and aggregation",
            ),
            # For date range filtering with driver category
            (
                "fact_channel_contribution",
                "idx_fcc_date_driver_optimized",
                "(date_key, driver_category, store_key)",
                "Optimized index for date range filtering with driver category",
            ),
            # For store-level aggregations
            (
                "fact_channel_contribution",
                "idx_fcc_store_aggregation",
                "(store_key, driver_category, date_key)",
                "Optimized index for store-level aggregations",
            ),
        ]

        # Critical indexes for fact_spend (10s query optimization)
        spend_indexes = [
            # For spend data joins with channel contribution
            (
                "fact_spend",
                "idx_fs_channel_date_optimized",
                "(channel_key, date_key, store_key)",
                "Optimized index for spend data joins",
            ),
            # For spend aggregations by date and channel
            (
                "fact_spend",
                "idx_fs_aggregation_optimized",
                "(date_key, channel_key)",
                "Optimized index for spend aggregations",
            ),
        ]

        all_indexes = critical_indexes + spend_indexes

        logger.info(f"Creating {len(all_indexes)} critical performance indexes...")

        success_count = 0
        skip_count = 0
        error_count = 0

        for table, index_name, definition, description in all_indexes:
            try:
                # Check if index already exists
                check_query = """
                SELECT COUNT(*) as count
                FROM INFORMATION_SCHEMA.STATISTICS
                WHERE TABLE_SCHEMA = DATABASE()
                AND TABLE_NAME = %s
                AND INDEX_NAME = %s
                """

                result = await connection.execute_query(check_query, [table, index_name])

                if result[0]["count"] == 0:
                    await connection.execute_query(
                        f"CREATE INDEX {index_name} ON {table} {definition}"
                    )
                    logger.info(f"Created critical index {index_name} on {table}")
                    success_count += 1
                else:
                    logger.info(f"Index {index_name} already exists on {table}")
                    skip_count += 1

            except Exception as e:
                logger.error(f"Failed to create index {index_name}: {e}")
                error_count += 1

        logger.info("Critical index creation completed")
        logger.info(f"  Created: {success_count}")
        logger.info(f"  Skipped: {skip_count}")
        logger.info(f"  Failed: {error_count}")

        if error_count == 0:
            logger.info("All critical indexes are ready")
            logger.info("Slowest queries should be much faster now")
        else:
            logger.warning(f"{error_count} indexes failed, check logs above")

        return error_count == 0

    except Exception as e:
        logger.error(f"Something went wrong with performance optimizations: {e}")
        return False

    finally:
        await close_db()


async def main():
    """Main function."""
    logger.info("Critical Performance Optimization")
    logger.info("=" * 50)
    logger.info("This will create critical indexes to speed up the slowest queries:")
    logger.info("  • get_driver_data: 10s -> <2s")
    logger.info("  • get_all_channel_contributions: 7s -> <1s")

    response = input("\nContinue? (y/N): ")
    if response.lower() in ["y", "yes"]:
        success = await apply_performance_optimizations()
        if success:
            logger.info("Critical performance optimization completed")
            logger.info("Performance service should be significantly faster now")
        else:
            logger.error("Some optimizations failed, check logs above")
        sys.exit(0 if success else 1)
    else:
        logger.info("Cancelled")


if __name__ == "__main__":
    asyncio.run(main())

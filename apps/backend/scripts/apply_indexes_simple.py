#!/usr/bin/env python3
"""
Simple script to apply performance indexes using direct MySQL connection.
"""

import os
import sys
import mysql.connector
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables from the project root .env file
project_root = Path(__file__).parent.parent.parent.parent
env_path = project_root / ".env"
load_dotenv(env_path)


def get_db_config():
    """Get database configuration from environment variables."""
    # If running outside Docker, connect to localhost instead of 'mysql'
    host = os.getenv("MYSQL_HOST", "mysql")
    if host == "mysql":
        host = "localhost"  # Docker service name -> localhost when running outside Docker

    return {
        "host": host,
        "port": int(os.getenv("MYSQL_PORT", "3306")),
        "user": os.getenv("MYSQL_USER", "app_user"),
        "password": os.getenv("MYSQL_PASSWORD", "password"),
        "database": os.getenv("MYSQL_DB", "app_db"),
    }


def read_sql_file():
    """Read the SQL file and extract individual statements."""
    sql_file_path = Path(__file__).parent.parent / "migrations" / "add_performance_indexes.sql"

    if not sql_file_path.exists():
        raise FileNotFoundError(f"SQL file not found: {sql_file_path}")

    with open(sql_file_path, "r") as f:
        content = f.read()

    # Split by semicolons and filter out comments and empty statements
    statements = []
    for statement in content.split(";"):
        # Clean up the statement - remove comments and extra whitespace
        lines = []
        for line in statement.split("\n"):
            line = line.strip()
            if line and not line.startswith("--"):
                lines.append(line)

        cleaned_statement = " ".join(lines)
        if cleaned_statement and "CREATE INDEX" in cleaned_statement:
            statements.append(cleaned_statement + ";")

    return statements


def apply_indexes():
    """Apply all performance indexes to the database."""
    try:
        # Get database configuration
        db_config = get_db_config()

        print("Connecting to database...")
        print(f"Host: {db_config['host']}:{db_config['port']}")
        print(f"Database: {db_config['database']}")

        # Connect to database
        connection = mysql.connector.connect(**db_config)
        cursor = connection.cursor()

        # Read SQL statements from file
        statements = read_sql_file()

        print(f"\nGot {len(statements)} indexes to create...")

        success_count = 0
        error_count = 0

        for i, statement in enumerate(statements, 1):
            try:
                print(f"Creating index {i}/{len(statements)}...")
                cursor.execute(statement)
                success_count += 1
                print(f"Done with index {i}")

            except mysql.connector.Error as e:
                error_count += 1
                print(f"Index {i} failed: {e}")

        # Commit changes
        connection.commit()

        print(f"\nIndex creation finished:")
        print(f"  Created: {success_count}")
        print(f"  Failed: {error_count}")
        print(f"  Total: {len(statements)}")

        if error_count == 0:
            print("\nAll indexes created")
            print("Performance should be better now")
        else:
            print(f"\n{error_count} indexes had issues, check above")

        return error_count == 0

    except mysql.connector.Error as e:
        print(f"Database connection failed: {e}")
        print("\nCheck that:")
        print("  • Database is running")
        print("  • Connection credentials are correct")
        print("  • Database exists")
        return False

    except Exception as e:
        print(f"Something went wrong: {e}")
        return False

    finally:
        if "connection" in locals() and connection.is_connected():
            cursor.close()
            connection.close()
            print("\nDisconnected from database")


def check_existing_indexes():
    """Check which indexes already exist in the database."""
    try:
        db_config = get_db_config()
        connection = mysql.connector.connect(**db_config)
        cursor = connection.cursor()

        print("Checking existing indexes...")

        # Query to get all indexes on our target tables
        query = """
        SELECT
            TABLE_NAME,
            INDEX_NAME,
            COLUMN_NAME,
            NON_UNIQUE
        FROM INFORMATION_SCHEMA.STATISTICS
        WHERE TABLE_SCHEMA = %s
        AND TABLE_NAME IN (
            'fact_channel_contribution',
            'fact_weekly_sales',
            'fact_spend',
            'fact_response_curve',
            'dim_channel',
            'dim_date',
            'dim_store'
        )
        AND INDEX_NAME LIKE 'idx_%'
        ORDER BY TABLE_NAME, INDEX_NAME, SEQ_IN_INDEX
        """

        cursor.execute(query, (db_config["database"],))
        results = cursor.fetchall()

        if results:
            print("\nCurrent performance indexes:")
            current_table = None
            for row in results:
                table_name, index_name, column_name, non_unique = row
                if table_name != current_table:
                    current_table = table_name
                    print(f"\n{current_table}:")

                unique_str = "UNIQUE" if non_unique == 0 else "INDEX"
                print(f"  • {unique_str}: {index_name} ({column_name})")
        else:
            print("\nNo performance indexes found")
            print("Run without --check to create them")

    except mysql.connector.Error as e:
        print(f"Couldn't check indexes: {e}")
    finally:
        if "connection" in locals() and connection.is_connected():
            cursor.close()
            connection.close()


def show_dry_run():
    """Show what indexes would be applied without actually applying them."""
    try:
        statements = read_sql_file()
        print("Dry run - showing what would be created:")
        print("=" * 60)
        for i, statement in enumerate(statements, 1):
            print(f"\n{i}. {statement}")
        print("=" * 60)
        print(f"\nWould create {len(statements)} indexes")
    except Exception as e:
        print(f"Couldn't read SQL file: {e}")


def main():
    """Main function."""
    import argparse

    parser = argparse.ArgumentParser(description="Apply performance indexes to the database")
    parser.add_argument("--check", action="store_true", help="Check existing indexes")
    parser.add_argument("--dry-run", action="store_true", help="Show what would be applied")

    args = parser.parse_args()

    print("Database Performance Index Creation")
    print("=" * 50)

    if args.check:
        check_existing_indexes()
    elif args.dry_run:
        show_dry_run()
    else:
        print("This will create database indexes to improve performance.")
        print("Use --check to see what's already there.")
        print("Use --dry-run to preview what would be created.")

        response = input("\nContinue? (y/N): ")
        if response.lower() in ["y", "yes"]:
            success = apply_indexes()
            sys.exit(0 if success else 1)
        else:
            print("Cancelled")


if __name__ == "__main__":
    main()

#!/bin/bash

# Initialize a variable to keep track of the overall exit status
EXIT_STATUS=0

# Function to run a command and check its exit status
run_command() {
    echo "Running: $*"
    "$@"
    local status=$?
    if [ $status -ne 0 ]; then
        echo "Error with '$*'"
        EXIT_STATUS=$status
    fi
}

# Determine mode based on argument
MODE="check"
if [[ "$1" == "--fix" ]]; then
    MODE="fix"
fi

echo "Running in $MODE mode..."

if [[ "$MODE" == "check" ]]; then
    # Example usage; these tools may not have a direct "check" mode equivalent
    run_command ruff check .
    run_command black --check .
    run_command isort --check-only .
else
    run_command ruff check . --fix
    run_command black .
    run_command isort .
    run_command blacken-docs
fi

# Exit with the accumulated exit status
exit $EXIT_STATUS
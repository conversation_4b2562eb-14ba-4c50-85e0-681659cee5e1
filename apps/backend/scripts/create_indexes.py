#!/usr/bin/env python3
"""
Standalone script to create performance indexes.
This can be run independently of the full pipeline.
"""

import asyncio
import sys
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.shared.services.database_service import init_db, close_db
from src.shared.pipelines.indexes_to_db import indexes_to_db
from src.shared.services.logger_app_service import get_logger

logger = get_logger()


async def main():
    """Main function to create performance indexes."""
    logger.info("Performance Index Creation")
    logger.info("=" * 50)

    try:
        # Initialize database connection
        logger.info("Connecting to database...")
        await init_db()

        # Create performance indexes
        await indexes_to_db()

        logger.info("Performance index creation completed")

    except Exception as e:
        logger.error(f"Something went wrong creating indexes: {e}")
        sys.exit(1)

    finally:
        # Clean up database connections
        try:
            await close_db()
        except Exception as e:
            logger.warning(f"Error closing database connections: {e}")


if __name__ == "__main__":
    asyncio.run(main())

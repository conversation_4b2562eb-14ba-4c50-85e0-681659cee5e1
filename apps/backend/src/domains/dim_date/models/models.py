from tortoise import models, fields


class DimDate(models.Model):
    date_key = fields.IntField(pk=True)
    date = fields.DatetimeField()
    day_of_week = fields.CharField(max_length=20)
    month = fields.IntField()
    quarter = fields.IntField()
    year = fields.IntField()
    is_holiday = fields.BooleanField()
    season = fields.CharField(max_length=20)

    class Meta:
        table = "dim_date"

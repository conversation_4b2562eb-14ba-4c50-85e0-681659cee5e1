from typing import TYPE_CHECKING
from tortoise import fields, models

if TYPE_CHECKING:
    from src.domains.product.models.models import Product


class Channel(models.Model):
    id = fields.IntField(pk=True)
    channel_name = fields.CharField(max_length=255)
    products: fields.ReverseRelation["Product"]

    created_at = fields.DatetimeField(auto_now_add=True)
    updated_at = fields.DatetimeField(auto_now=True)

    class Meta:
        table = "channel"
        ordering = ["channel_name"]

    def __str__(self):
        return self.channel_name

from src.domains.channel.cqrs.queries.get_channels_with_products_query import (
    GetChannelsWithProductsQuery,
)
from src.shared.factories.base_router_factory import BaseRouterFactory
from src.shared.di.container import MainContainer
from src.shared.services.logger_app_service import get_logger
from fastapi import APIRouter

logger = get_logger()


class ChannelRouterFactory(BaseRouterFactory):
    """Factory for creating channel routers"""

    def get_router(self, container: MainContainer) -> APIRouter:
        """Create and return a configured channel router."""
        router = APIRouter(prefix="/channels")

        try:
            query_bus = container.query_bus()

            # Register handler explicitly
            query_bus.register(
                GetChannelsWithProductsQuery,
                container.channel_container.get_channels_with_products(),
            )

            @router.get("")
            async def get_channels():
                query = GetChannelsWithProductsQuery()
                return await query_bus.execute(query)

        except Exception as e:
            # Log unexpected errors to help debugging
            logger.error(f"Error creating channel router: {str(e)}", exc_info=True)
            raise RuntimeError("Failed to create channel router") from e

        return router

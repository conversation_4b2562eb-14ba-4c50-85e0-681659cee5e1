from dependency_injector.wiring import inject
from src.shared.bus.base_query_handler import BaseQueryHandler
from src.shared.services.logger_app_service import get_logger
from src.domains.brand.services.brand_service import BrandService
from src.domains.brand.cqrs.queries.get_brands_with_products_query import (
    GetBrandsWithProductsQuery,
)

logger = get_logger()


class GetBrandsWithProductsHandler(BaseQueryHandler[GetBrandsWithProductsQuery, list[dict]]):
    @inject
    def __init__(self, brand_service: BrandService):
        self._brand_service = brand_service

    async def execute(self, query: GetBrandsWithProductsQuery) -> list[dict]:
        try:
            logger.info("Executing GetBrandsWithProductsHandler")
            value = await self._brand_service.get_brands_with_products()
            logger.debug(f"Fetched value: {value}")
            return value
        except Exception as e:
            logger.error(f"Error during execution: {e}")
            raise

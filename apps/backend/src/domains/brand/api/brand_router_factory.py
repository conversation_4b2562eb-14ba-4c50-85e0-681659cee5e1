from src.domains.brand.cqrs.queries.get_brands_with_products_query import (
    GetBrandsWithProductsQuery,
)
from src.shared.factories.base_router_factory import BaseRouterFactory
from src.shared.di.container import MainContainer
from src.shared.services.logger_app_service import get_logger
from fastapi import APIRouter

logger = get_logger()


class BrandRouterFactory(BaseRouterFactory):
    """Factory for creating brand routers"""

    def get_router(self, container: MainContainer) -> APIRouter:
        """Create and return a configured brand router."""
        router = APIRouter(prefix="/brands")

        try:
            query_bus = container.query_bus()

            # Register handler explicitly
            query_bus.register(
                GetBrandsWithProductsQuery,
                container.brand_container.get_brands_with_products(),
            )

            @router.get("")
            async def get_brands():
                query = GetBrandsWithProductsQuery()
                return await query_bus.execute(query)

        except Exception as e:
            # Log unexpected errors to help debugging
            logger.error(f"Error creating brand router: {str(e)}", exc_info=True)
            raise RuntimeError("Failed to create brand router") from e

        return router

from dependency_injector import containers, providers
from src.domains.brand.cqrs.queries.handlers.get_brands_with_products_handler import (
    GetBrandsWithProductsHandler,
)
from src.domains.brand.services.brand_service import BrandService


class BrandContainer(containers.DeclarativeContainer):
    """Container for brand-related dependencies."""

    config = providers.Configuration()

    # Service layer
    brand_service = providers.Singleton(BrandService)

    # Handler layer (wired with services)
    get_brands_with_products = providers.Singleton(
        GetBrandsWithProductsHandler,
        brand_service=brand_service,
    )

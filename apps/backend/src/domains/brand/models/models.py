from typing import TYPE_CHECKING
from tortoise import models, fields

if TYPE_CHECKING:
    from src.domains.product.models.models import Product


class Brand(models.Model):
    id = fields.IntField(pk=True)
    brand_name = fields.CharField(max_length=255)
    products: fields.ReverseRelation["Product"]

    created_at = fields.DatetimeField(auto_now_add=True)
    updated_at = fields.DatetimeField(auto_now=True)

    class Meta:
        table = "brand"
        ordering = ["brand_name"]

    def __str__(self):
        return self.brand_name

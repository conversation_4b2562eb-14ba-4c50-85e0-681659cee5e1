from src.domains.brand.models.models import Brand
from src.shared.services.logger_app_service import get_logger

logger = get_logger()


class BrandService:
    """Brand service for handling general operations"""

    async def get_brands_with_products(self) -> list[dict]:
        """Returns all brands with their associated products"""
        try:
            brands = await Brand.all().prefetch_related("products")

            result = []
            for brand in brands:
                result.append(
                    {
                        "id": brand.id,
                        "brand_name": brand.brand_name,
                        "products": [
                            {"id": product.id, "product_name": product.product_name}
                            for product in brand.products
                        ],
                    }
                )

            return result

        except Exception as e:
            logger.error(f"Error retrieving brands with products: {e}", exc_info=True)
            raise

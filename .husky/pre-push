#!/bin/sh
echo "Running pre-push hook..."

# Check for staged files
staged_files=$(git diff --cached --diff-filter=ACMRT --name-only dev)
if [ -z "$staged_files" ]; then
  echo "No staged files to check."
  exit 0
fi

# Step 1: Check formatting
echo "Checking formatting..."
if ! yarn nx format:check --base=dev; then
  echo "Formatting issues found. Please fix them before pushing."

  # Show the detailed errors by running `yarn nx format`
  echo "Suggested Fix:"
  yarn nx format --base=dev

  exit 1
fi

# Step 2: Lint files
echo "Running lint checks..."
yarn nx run-many -t lint || {
  echo "Linting issues found. Please fix them before pushing."
  exit 1
}

# Step 3: Run Pylint for Python files (if any)
python_files=$(echo "$staged_files" | grep 'apps/backend/.*\.py$' || true)
if [ -n "$python_files" ]; then
  echo "Running Pylint on Python files..."
  pylint --rcfile=apps/backend/.pylintrc $python_files || {
    echo "Pylint failed. Fix the issues before pushing."
    exit 1
  }
fi

echo "Pre-push hook completed successfully."
